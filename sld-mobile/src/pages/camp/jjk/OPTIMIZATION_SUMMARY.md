# 金句卡组件优化总结

## 优化内容

### 1. 接口调用优化
- **原接口**: `front/camp/resource` (POST/GET)
- **新接口**: `/v3/promotion/front/cms/get` (GET)
- **优化原因**: 根据API文档要求，使用正确的CMS接口获取表单数据

### 2. 抽卡功能优化 (`drawCard()` 方法)
- 修改接口URL为 `/v3/promotion/front/cms/get`
- 改为GET请求方式
- 保持 `groupKey` 参数，不传 `rid` 参数让服务端随机返回
- 增加数据验证，确保返回的数据包含必要字段 (`rid` 和 `imageUrl`)
- 增加详细的错误日志和调试信息
- 保留原有的fallback机制，接口失败时使用模拟数据

### 3. 卡片加载功能优化 (`loadCardByRid()` 方法)
- 修改接口URL为 `/v3/promotion/front/cms/get`
- 增加数据字段的兼容性处理：
  - `imageUrl || image || url` (图片字段)
  - `rid || id || formKey` (ID字段)
- 增加数据验证和错误处理
- 增加详细的日志输出

### 4. 数据结构兼容性
由于新接口的数据结构可能与原接口不同，增加了多种字段的兼容性处理：

```javascript
// ID字段兼容
const rid = resourceData.rid || resourceData.id || resourceData.formKey

// 图片字段兼容  
const imageUrl = resourceData.imageUrl || resourceData.image || resourceData.url
```

### 5. 错误处理增强
- 增加数据完整性验证
- 增加详细的错误日志
- 改进用户提示信息
- 保留原有的模拟数据fallback机制

## 配置说明

### groupKey 配置
```javascript
groupKey: 'NzfYPDDe' // 彩虹卡资源组 - 用于CMS接口的表单组key
```

这个key用于CMS接口的 `groupKey` 参数，用于随机获取该组下的表单数据。

## API接口说明

### 新接口: `/v3/promotion/front/cms/get`
- **请求方式**: GET
- **参数**:
  - `groupKey`: 表单组key，用于随机获取该组下的表单数据
  - `rid`: 表单记录ID（可选，不传则随机返回）
- **响应格式**: 
  ```javascript
  {
    "data": {}, // 表单数据对象
    "msg": "",
    "state": 200, // 200==成功
    "timestamp": 0
  }
  ```

## 保持不变的功能
- UI交互逻辑
- 卡片翻转动画
- 粒子效果
- 海报生成功能
- 分享功能
- URL参数更新机制

## 测试建议
1. 测试正常抽卡流程
2. 测试通过URL参数加载特定卡片
3. 测试接口异常时的fallback机制
4. 验证新接口返回的数据结构
5. 检查控制台日志输出是否正常
